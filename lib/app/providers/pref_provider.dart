import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:package_info_plus/package_info_plus.dart';

import '../../enums.dart';
import 'box_provider.dart';

class PrefProvider {
  final BoxProvider boxProvider;
  final DeviceInfoPlugin deviceInfo;
  final PackageInfo packageInfo;

  // 响应式主题模式
  final _currentThemeMode = ThemeMode.system.obs;

  // 响应式语言设置
  final _currentLocale = const Locale('zh', 'TW').obs;

  PrefProvider({
    required this.boxProvider,
    required this.deviceInfo,
    required this.packageInfo,
  }) {
    _loadThemeMode();
    _loadLocale();
  }

  /// 获取当前主题模式（响应式）
  ThemeMode get currentThemeMode => _currentThemeMode.value;

  /// 获取当前语言设置（响应式）
  Locale get currentLocale => _currentLocale.value;

  set themeMode(ThemeMode value) {
    // 更新响应式变量
    _currentThemeMode.value = value;
    // 持久化存储
    boxProvider.getGsBox(Boxes.settings.name).write('themeMode', value.name);
    // 更新应用主题
    Get.changeThemeMode(value);
  }

  set locale(Locale value) {
    // 更新响应式变量
    _currentLocale.value = value;
    // 持久化存储
    boxProvider.getGsBox(Boxes.settings.name).write('locale', '${value.languageCode}_${value.countryCode}');
    // 更新应用语言
    Get.updateLocale(value);
  }

  ThemeMode get themeMode {
    final mode = boxProvider.getGsBox(Boxes.settings.name).read('themeMode');
    if (mode == null) return ThemeMode.system;
    return ThemeMode.values.firstWhere(
      (e) => e.name == mode,
      orElse: () => ThemeMode.system,
    );
  }

  Locale get locale {
    final localeString = boxProvider.getGsBox(Boxes.settings.name).read('locale');
    if (localeString == null) return const Locale('zh', 'TW');

    final parts = localeString.split('_');
    if (parts.length == 2) {
      return Locale(parts[0], parts[1]);
    }
    return const Locale('zh', 'TW');
  }

  /// 加载保存的主题模式
  void _loadThemeMode() {
    final mode = boxProvider.getGsBox(Boxes.settings.name).read('themeMode');
    if (mode != null) {
      final themeMode = ThemeMode.values.firstWhere(
        (e) => e.name == mode,
        orElse: () => ThemeMode.system,
      );
      // 更新响应式变量
      _currentThemeMode.value = themeMode;
      // 更新应用主题
      Future(() => Get.changeThemeMode(themeMode));
    } else {
      // 如果没有保存的主题模式，使用系统默认
      _currentThemeMode.value = ThemeMode.system;
    }
  }

  /// 加载保存的语言设置
  void _loadLocale() {
    final localeString = boxProvider.getGsBox(Boxes.settings.name).read('locale');
    if (localeString != null) {
      final parts = localeString.split('_');
      if (parts.length == 2) {
        final locale = Locale(parts[0], parts[1]);
        // 更新响应式变量
        _currentLocale.value = locale;
        // 更新应用语言
        Future(() => Get.updateLocale(locale));
      } else {
        // 如果格式不正确，使用默认语言
        _currentLocale.value = const Locale('zh', 'TW');
      }
    } else {
      // 如果没有保存的语言设置，使用默认语言
      _currentLocale.value = const Locale('zh', 'TW');
    }
  }

  /// 获取当前语言显示名称
  String get currentLocaleDisplayName {
    return getLocaleDisplayName(_currentLocale.value);
  }

  /// 获取语言显示名称
  String getLocaleDisplayName(Locale locale) {
    switch ('${locale.languageCode}_${locale.countryCode}') {
      case 'en_US':
        return 'language_english'.tr;
      case 'zh_TW':
        return 'language_chinese'.tr;
      default:
        return 'language_chinese'.tr;
    }
  }

  /// 获取支持的语言列表
  List<Locale> get supportedLocales => const [
    Locale('en', 'US'),
    Locale('zh', 'TW'),
  ];
}
